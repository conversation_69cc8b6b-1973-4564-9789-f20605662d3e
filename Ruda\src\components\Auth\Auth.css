.auth-container {
  min-height: 100vh;
  background-color: #4a5568;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', '<PERSON>xy<PERSON>',
    '<PERSON>bunt<PERSON>', '<PERSON><PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}

.auth-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 450px;
  min-width: 350px;
}

.auth-title {
  text-align: center;
  margin-bottom: 30px;
  color: #2d3748;
  font-size: 28px;
  font-weight: 600;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #4a5568;
  font-size: 14px;
}

.form-group input {
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s ease;
  outline: none;
}

.form-group input:focus {
  border-color: #4a5568;
  box-shadow: 0 0 0 3px rgba(74, 85, 104, 0.1);
}

.form-group input.error {
  border-color: #e53e3e;
}

.form-group input.error:focus {
  border-color: #e53e3e;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.error-text {
  color: #e53e3e;
  font-size: 12px;
  margin-top: 4px;
}

.submit-error {
  text-align: center;
  margin: 10px 0;
  padding: 10px;
  background-color: #fed7d7;
  border-radius: 6px;
  border: 1px solid #feb2b2;
}

.auth-button {
  background-color: #4a5568;
  color: white;
  border: none;
  padding: 14px 20px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 10px;
}

.auth-button:hover:not(:disabled) {
  background-color: #2d3748;
}

.auth-button:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}

.auth-link {
  text-align: center;
  margin-top: 20px;
  color: #718096;
  font-size: 14px;
}

.link-text {
  color: #4a5568;
  cursor: pointer;
  font-weight: 600;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.link-text:hover {
  color: #2d3748;
}

/* Responsive design */
@media (max-width: 480px) {
  .auth-container {
    padding: 10px;
  }
  
  .auth-card {
    padding: 30px 20px;
    min-width: unset;
  }
  
  .auth-title {
    font-size: 24px;
  }
}
